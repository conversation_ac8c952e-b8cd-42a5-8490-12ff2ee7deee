<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON> Profesional</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.4;
            color: #2d3748;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            display: grid;
            grid-template-columns: 200px 1fr;
            min-height: 297mm;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 30px 20px;
            border-right: 1px solid #e9ecef;
        }
        
        .profile-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .photo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #6c757d;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            font-weight: 600;
            color: white;
        }
        
        .name {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #2d3748;
        }
        
        .title {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        
        .contact-info {
            margin-bottom: 25px;
        }
        
        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            margin-bottom: 10px;
            font-size: 0.7rem;
            line-height: 1.3;
        }
        
        .contact-icon {
            width: 12px;
            height: 12px;
            fill: #6c757d;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .sidebar-section {
            margin-bottom: 25px;
        }
        
        .sidebar-title {
            font-size: 0.8rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #2d3748;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .education-item {
            margin-bottom: 15px;
        }
        
        .degree {
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 3px;
            color: #2d3748;
            line-height: 1.2;
        }
        
        .university {
            font-size: 0.7rem;
            color: #6c757d;
            margin-bottom: 2px;
            line-height: 1.2;
        }
        
        .date-small {
            font-size: 0.65rem;
            color: #6c757d;
        }
        
        .skill-item {
            margin-bottom: 15px;
        }
        
        .skill-name {
            font-size: 0.7rem;
            font-weight: 500;
            margin-bottom: 5px;
            line-height: 1.2;
        }
        
        .skill-bar {
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .skill-progress {
            height: 100%;
            background: #6c757d;
            border-radius: 2px;
        }
        
        .profile-text-sidebar {
            font-size: 0.7rem;
            line-height: 1.4;
            color: #495057;
            text-align: justify;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 30px 25px;
            background: white;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .experience-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .experience-item:last-child {
            border-bottom: none;
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .job-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
        }
        
        .company {
            font-size: 0.8rem;
            color: #6c757d;
            font-weight: 500;
        }
        
        .date {
            font-size: 0.7rem;
            color: #6c757d;
            white-space: nowrap;
        }
        
        .job-description {
            list-style: none;
            padding: 0;
        }
        
        .job-description li {
            margin-bottom: 4px;
            padding-left: 12px;
            position: relative;
            color: #495057;
            font-size: 0.75rem;
            line-height: 1.3;
        }
        
        .job-description li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #6c757d;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .tool-item {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .tool-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
            font-size: 0.75rem;
        }
        
        .tool-description {
            font-size: 0.65rem;
            color: #6c757d;
            margin-bottom: 6px;
            line-height: 1.2;
        }
        
        .tool-link {
            color: #007bff;
            text-decoration: none;
            font-size: 0.65rem;
        }
        
        .highlight-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 3px solid #6c757d;
        }
        
        .highlight-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .highlight-text {
            font-size: 0.75rem;
            line-height: 1.3;
            color: #495057;
        }
        
        .pdf-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
            z-index: 1000;
        }
        
        .pdf-button:hover {
            background: #0056b3;
        }
        
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            body {
                background: white;
                padding: 0;
                margin: 0;
            }
            
            .container {
                max-width: none;
                margin: 0;
                box-shadow: none;
                min-height: auto;
                height: 100vh;
                overflow: hidden;
            }
            
            .pdf-button {
                display: none;
            }
            
            .sidebar {
                background: #f8f9fa !important;
            }
            
            .tool-item {
                background: #f8f9fa !important;
            }
            
            .highlight-box {
                background: #f8f9fa !important;
            }
        }
    </style>
</head>
<body>
    <button class="pdf-button" onclick="window.print()">📄 Exportar PDF</button>
    
    <div class="container">
        <aside class="sidebar">
            <div class="profile-section">
                <div class="photo-placeholder">EC</div>
                <h1 class="name">Efren Cruz</h1>
                <p class="title">Especialista en Nómina & Tecnología</p>
            </div>
            
            <div class="contact-info">
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <span>San Pedro Tlaquepaque, Jalisco</span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                    </svg>
                    <span>+52 331 161 1322</span>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3 class="sidebar-title">Formación</h3>
                <div class="education-item">
                    <h4 class="degree">Lic. Contaduría Pública</h4>
                    <p class="university">Universidad de Guadalajara</p>
                    <p class="date-small">2006 - 2010</p>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3 class="sidebar-title">Habilidades</h3>
                <div class="skill-item">
                    <div class="skill-name">Cálculo de Nómina</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 100%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">IMSS, Infonavit, Fonacot</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 100%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">REPSE, ICSOE, SISUB</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 95%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">SQL, Python, Power BI</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 80%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">Liderazgo de Equipos</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 90%"></div>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3 class="sidebar-title">Perfil Profesional</h3>
                <p class="profile-text-sidebar">
                    Especialista en nómina con más de 10 años de experiencia integral en todo el ciclo: pre-nómina, cálculos, finiquitos y cumplimiento ante IMSS, Infonavit, Fonacot, ISR, ISN, REPSE, ICSOE, SISUB y SIROC.
                </p>
                <p class="profile-text-sidebar">
                    He liderado equipos, creado áreas desde cero y acompañado a clientes en esquemas de subcontratación y maquila. Convencido de que la tecnología debe ayudar a lograr una nómina feliz… o al menos, sin quejas.
                </p>
            </div>
        </aside>

        <main class="main-content">
            <section class="section">
                <h2 class="section-title">Experiencia Profesional</h2>
                
                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Especialista en Nómina</h3>
                            <p class="company">Freelance</p>
                        </div>
                        <span class="date">Dic 2024 – Actualidad</span>
                    </div>
                    <ul class="job-description">
                        <li>Asesor en cumplimiento y soporte técnico para desarrollo del software <strong>Lynx Cloud</strong></li>
                        <li>Validación de cálculos de nómina y definición de requisitos funcionales</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Coordinador de Nóminas</h3>
                            <p class="company">GC Studio</p>
                        </div>
                        <span class="date">Ago 2021 – Nov 2024</span>
                    </div>
                    <ul class="job-description">
                        <li>Responsable del área de nóminas en despacho fiscal</li>
                        <li>Creación del departamento y gestión bajo maquila</li>
                        <li>Asesoría y cumplimiento ante IMSS, INFONAVIT, INFONACOT, ISN, REPSE, ICSOE y SISUB</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Gerente de Nóminas</h3>
                            <p class="company">Corporativo BOSSC</p>
                        </div>
                        <span class="date">May 2017 – Ago 2021</span>
                    </div>
                    <ul class="job-description">
                        <li>Funciones equivalentes a GC Studio</li>
                        <li>Coordinación de equipo y atención directa a clientes</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Supervisor de Nóminas</h3>
                            <p class="company">Human Services 21</p>
                        </div>
                        <span class="date">Oct 2011 – May 2017</span>
                    </div>
                    <ul class="job-description">
                        <li>Promovido de analista a supervisor</li>
                        <li>Coordinación de 4 analistas y validación de nóminas especiales</li>
                        <li>Control de headcount vinculado al cobro del servicio</li>
                    </ul>
                </div>
            </section>

            <section class="section">
                <h2 class="section-title">Proyectos e Intereses</h2>
                <div class="tools-grid">
                    <div class="tool-item">
                        <h3 class="tool-name">PTU Tool</h3>
                        <p class="tool-description">Herramienta para cálculo de Participación de Trabajadores en las Utilidades</p>
                        <a href="https://ptu-tool.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                    <div class="tool-item">
                        <h3 class="tool-name">Nominante DIOT</h3>
                        <p class="tool-description">Herramienta para Declaración Informativa de Operaciones con Terceros</p>
                        <a href="https://nominantediot.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                </div>
                <div class="highlight-box">
                    <h3 class="highlight-title">Enfoque Profesional</h3>
                    <p class="highlight-text">
                        Automatización y eficiencia en procesos de nómina con mejora continua aplicando tecnología.
                    </p>
                </div>
            </section>
        </main>
    </div>
</body>
</html>
