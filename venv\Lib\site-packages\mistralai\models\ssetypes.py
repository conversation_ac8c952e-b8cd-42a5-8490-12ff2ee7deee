"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from typing import Literal


SSETypes = Literal[
    "conversation.response.started",
    "conversation.response.done",
    "conversation.response.error",
    "message.output.delta",
    "tool.execution.started",
    "tool.execution.done",
    "agent.handoff.started",
    "agent.handoff.done",
    "function.call.delta",
]
r"""Server side events sent when streaming a conversation response."""
