"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .codeinterpretertool import CodeInterpreterTool, CodeInterpreterToolTypedDict
from .completionargs import CompletionArgs, CompletionArgsTypedDict
from .documentlibrarytool import DocumentLibraryTool, DocumentLibraryToolTypedDict
from .functiontool import FunctionTool, FunctionToolTypedDict
from .imagegenerationtool import ImageGenerationTool, ImageGenerationToolTypedDict
from .websearchpremiumtool import WebSearchPremiumTool, WebSearchPremiumToolTypedDict
from .websearchtool import WebSearchTool, WebSearchToolTypedDict
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag, model_serializer
from typing import List, Literal, Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


AgentToolsTypedDict = TypeAliasType(
    "AgentToolsTypedDict",
    Union[
        WebSearchToolTypedDict,
        WebSearchPremiumToolTypedDict,
        CodeInterpreterToolTypedDict,
        ImageGenerationToolTypedDict,
        FunctionToolTypedDict,
        DocumentLibraryToolTypedDict,
    ],
)


AgentTools = Annotated[
    Union[
        Annotated[CodeInterpreterTool, Tag("code_interpreter")],
        Annotated[DocumentLibraryTool, Tag("document_library")],
        Annotated[FunctionTool, Tag("function")],
        Annotated[ImageGenerationTool, Tag("image_generation")],
        Annotated[WebSearchTool, Tag("web_search")],
        Annotated[WebSearchPremiumTool, Tag("web_search_premium")],
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]


AgentObject = Literal["agent"]


class AgentTypedDict(TypedDict):
    model: str
    name: str
    id: str
    version: int
    created_at: datetime
    updated_at: datetime
    instructions: NotRequired[Nullable[str]]
    r"""Instruction prompt the model will follow during the conversation."""
    tools: NotRequired[List[AgentToolsTypedDict]]
    r"""List of tools which are available to the model during the conversation."""
    completion_args: NotRequired[CompletionArgsTypedDict]
    r"""White-listed arguments from the completion API"""
    description: NotRequired[Nullable[str]]
    handoffs: NotRequired[Nullable[List[str]]]
    object: NotRequired[AgentObject]


class Agent(BaseModel):
    model: str

    name: str

    id: str

    version: int

    created_at: datetime

    updated_at: datetime

    instructions: OptionalNullable[str] = UNSET
    r"""Instruction prompt the model will follow during the conversation."""

    tools: Optional[List[AgentTools]] = None
    r"""List of tools which are available to the model during the conversation."""

    completion_args: Optional[CompletionArgs] = None
    r"""White-listed arguments from the completion API"""

    description: OptionalNullable[str] = UNSET

    handoffs: OptionalNullable[List[str]] = UNSET

    object: Optional[AgentObject] = "agent"

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "instructions",
            "tools",
            "completion_args",
            "description",
            "handoffs",
            "object",
        ]
        nullable_fields = ["instructions", "description", "handoffs"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
