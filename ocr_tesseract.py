import pytesseract
from pdf2image import convert_from_path
from PIL import Image
import os
import sys

# Cambia esto si tu instalación de tesseract está en otra ruta
tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
pytesseract.pytesseract.tesseract_cmd = tesseract_path

PDF_FILE = "ActadeNacimiento.pdf"

# Buscar Poppler automáticamente en ubicaciones comunes
poppler_candidates = [
    r"C:\poppler\Library\bin",
    r"C:\Program Files\poppler-23.11.0\Library\bin",
    r"C:\Program Files\poppler-0.68.0\bin",
    r"C:\Program Files\poppler\bin",
    r"C:\poppler\bin",
]
poppler_path = None
for candidate in poppler_candidates:
    if os.path.exists(candidate):
        poppler_path = candidate
        break

if not poppler_path:
    print("❌ No se encontró Poppler en ubicaciones comunes.")
    print("Por favor, descarga Poppler para Windows desde:")
    print("https://github.com/oschwartz10612/poppler-windows/releases/")
    print("Descomprime el ZIP y copia la ruta de la carpeta 'bin'.")
    print("Luego, actualiza la variable 'poppler_candidates' en este script o mueve la carpeta a C:/poppler/Library/bin")
    sys.exit(1)

# Convertir PDF a imágenes (una por página)
pages = convert_from_path(PDF_FILE, dpi=300, poppler_path=poppler_path)

texto_completo = ""
for i, page in enumerate(pages):
    # Guardar la imagen temporalmente
    img_path = f"page_{i+1}.png"
    page.save(img_path, "PNG")
    # Extraer texto con Tesseract
    texto = pytesseract.image_to_string(Image.open(img_path), lang="spa")
    print(f"\n--- TEXTO DE LA PÁGINA {i+1} ---\n")
    print(texto)
    texto_completo += f"\n--- PÁGINA {i+1} ---\n{texto}\n"
    # Elimina la imagen temporal
    os.remove(img_path)

# Guardar todo el texto en un archivo
with open("texto_tesseract.txt", "w", encoding="utf-8") as f:
    f.write(texto_completo)

print("\n✅ Proceso completado. Revisa el archivo texto_tesseract.txt") 