from mistralai import Mistral
from dotenv import load_dotenv
import os
import mimetypes
import base64

load_dotenv()
api_key = os.environ["MISTRAL_API_KEY"]
client = Mistral(api_key=api_key)

# Cambia el nombre de la imagen aquí
IMAGE_FILE = "8d470866-fb40-4623-be03-10e9f6962272.jpg"  # O .png, etc.

def load_image(image_path):
    mime_type, _ = mimetypes.guess_type(image_path)
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
    base64_encoded = base64.b64encode(image_data).decode('utf-8')
    base64_url = f"data:{mime_type};base64,{base64_encoded}"
    return base64_url

# Codifica la imagen local como data URL
image_data_url = load_image(IMAGE_FILE)

ocr_response = client.ocr.process(
    model="mistral-ocr-latest",
    document={
        "type": "image_url",
        "image_url": image_data_url,
    },
    include_image_base64=True,
)

print("=== REGIONES DETECTADAS POR EL OCR ===")

for i, pagina in enumerate(ocr_response.pages):
    print(f"\n--- REGIÓN {i+1} ---")
    region_texto = ""
    # Markdown
    if hasattr(pagina, "markdown"):
        print("Markdown:")
        print(pagina.markdown)
        region_texto += f"\n[Markdown]\n{pagina.markdown}\n"
    # Texto plano
    if hasattr(pagina, "text"):
        print("Texto plano:")
        print(pagina.text)
        region_texto += f"\n[Texto plano]\n{pagina.text}\n"
    # Campos estructurados
    if hasattr(pagina, "fields") and pagina.fields:
        print("Campos:")
        print(pagina.fields)
        region_texto += f"\n[Campos]\n{str(pagina.fields)}\n"
    # Tablas
    if hasattr(pagina, "tables") and pagina.tables:
        print("Tablas:")
        print(pagina.tables)
        region_texto += f"\n[Tablas]\n{str(pagina.tables)}\n"
    # Imágenes
    if hasattr(pagina, "images") and pagina.images:
        print("Imágenes:")
        print(pagina.images)
        region_texto += f"\n[Imágenes]\n{str(pagina.images)}\n"
    # Guardar todo lo encontrado de la región
    nombre_archivo = f"region_{i+1}_completo.txt"
    with open(nombre_archivo, "w", encoding="utf-8") as archivo:
        archivo.write(region_texto)

print("\n✅ Proceso completado. Revisa los archivos region_X_completo.txt para ver todos los campos extraídos.") 