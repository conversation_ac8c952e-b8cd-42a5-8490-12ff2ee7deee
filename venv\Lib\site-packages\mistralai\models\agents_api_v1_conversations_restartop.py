"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .conversationrestartrequest import (
    ConversationRestartRequest,
    ConversationRestartRequestTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1ConversationsRestartRequestTypedDict(TypedDict):
    conversation_id: str
    conversation_restart_request: ConversationRestartRequestTypedDict


class AgentsAPIV1ConversationsRestartRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    conversation_restart_request: Annotated[
        ConversationRestartRequest,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
