from mistralai import Mistral
from dotenv import load_dotenv
import datauri
import os

load_dotenv()
api_key = os.environ["MISTRAL_API_KEY"]
client = Mistral(api_key=api_key)

def upload_pdf(filename):
  uploaded_pdf = client.files.upload(
    file={
      "file_name": filename,
      "content": open(filename, "rb"),
    },
    purpose="ocr"
  )
  signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)
  return signed_url.url

ocr_response = client.ocr.process(
  model="mistral-ocr-latest",
  document={
    "type": "document_url",
    "document_url": upload_pdf("ActadeNacimiento.pdf"),
  },
  include_image_base64=True,
)

# Extraer y guardar todos los campos posibles de cada región/página detectada por el OCR
print("=== REGIONES DETECTADAS POR EL OCR ===")

for i, pagina in enumerate(ocr_response.pages):
    print(f"\n--- REGIÓN {i+1} ---")
    region_texto = ""
    # Markdown
    if hasattr(pagina, "markdown"):
        print("Markdown:")
        print(pagina.markdown)
        region_texto += f"\n[Markdown]\n{pagina.markdown}\n"
    # Texto plano
    if hasattr(pagina, "text"):
        print("Texto plano:")
        print(pagina.text)
        region_texto += f"\n[Texto plano]\n{pagina.text}\n"
    # Campos estructurados
    if hasattr(pagina, "fields") and pagina.fields:
        print("Campos:")
        print(pagina.fields)
        region_texto += f"\n[Campos]\n{str(pagina.fields)}\n"
    # Tablas
    if hasattr(pagina, "tables") and pagina.tables:
        print("Tablas:")
        print(pagina.tables)
        region_texto += f"\n[Tablas]\n{str(pagina.tables)}\n"
    # Imágenes
    if hasattr(pagina, "images") and pagina.images:
        print("Imágenes:")
        print(pagina.images)
        region_texto += f"\n[Imágenes]\n{str(pagina.images)}\n"
    # Guardar todo lo encontrado de la región
    nombre_archivo = f"region_{i+1}_completo.txt"
    with open(nombre_archivo, "w", encoding="utf-8") as archivo:
        archivo.write(region_texto)

print("\n✅ Proceso completado. Revisa los archivos region_X_completo.txt para ver todos los campos extraídos.")