<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON> Profesional</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.4;
            color: #2d3748;
            background: white;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: calc(100vh - 40px);
            border: 1px solid #e2e8f0;
        }
        
        .sidebar {
            background: #f8fafc;
            color: #2d3748;
            padding: 40px 30px;
            border-right: 1px solid #e2e8f0;
        }
        
        .profile-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .photo-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: #4a5568;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 300;
            color: white;
            border: 4px solid #e2e8f0;
        }
        
        .name {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #2d3748;
        }
        
        .title {
            font-size: 1rem;
            font-weight: 400;
            color: #4a5568;
            margin-bottom: 30px;
        }
        
        .contact-info {
            margin-bottom: 40px;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            font-size: 0.9rem;
        }
        
        .contact-icon {
            width: 16px;
            height: 16px;
            fill: #4a5568;
            flex-shrink: 0;
        }
        
        .sidebar-section {
            margin-bottom: 35px;
        }
        
        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2d3748;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .education-item {
            margin-bottom: 20px;
        }
        
        .degree {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: #2d3748;
        }
        
        .university {
            font-size: 0.8rem;
            color: #4a5568;
            margin-bottom: 3px;
        }
        
        .date-small {
            font-size: 0.75rem;
            color: #718096;
        }
        
        .skill-item {
            margin-bottom: 20px;
        }
        
        .skill-name {
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .skill-bar {
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .skill-progress {
            height: 100%;
            background: #4a5568;
            border-radius: 3px;
            transition: width 2s ease;
        }
        
        .main-content {
            padding: 40px 35px;
            background: white;
        }
        
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4a5568;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background: #2d3748;
        }
        
        .profile-text {
            font-size: 0.95rem;
            line-height: 1.6;
            color: #4a5568;
            text-align: justify;
            margin-bottom: 15px;
        }
        
        .experience-item {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #4a5568;
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .job-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 3px;
        }
        
        .company {
            font-size: 0.9rem;
            color: #4a5568;
            font-weight: 500;
        }
        
        .date {
            font-size: 0.8rem;
            color: #718096;
            font-weight: 500;
            background: white;
            padding: 4px 12px;
            border-radius: 12px;
            white-space: nowrap;
            border: 1px solid #e2e8f0;
        }
        
        .job-description {
            list-style: none;
            padding: 0;
        }
        
        .job-description li {
            margin-bottom: 6px;
            padding-left: 20px;
            position: relative;
            color: #4a5568;
            font-size: 0.85rem;
            line-height: 1.4;
        }
        
        .job-description li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #4a5568;
            font-weight: bold;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .tool-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        
        .tool-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .tool-description {
            font-size: 0.75rem;
            color: #4a5568;
            margin-bottom: 8px;
            line-height: 1.3;
        }
        
        .tool-link {
            color: #4a5568;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.8rem;
        }
        
        .tool-link:hover {
            text-decoration: underline;
        }
        
        .highlight-box {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4a5568;
        }
        
        .highlight-title {
            font-size: 1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                max-width: none;
                min-height: auto;
                border-radius: 0;
            }
            
            .sidebar {
                padding: 30px 25px;
            }
            
            .main-content {
                padding: 30px 25px;
            }
            
            .section {
                margin-bottom: 25px;
            }
            
            .experience-item {
                margin-bottom: 20px;
                padding: 15px;
            }
            
            .profile-text {
                margin-bottom: 10px;
            }
            
            .tools-grid {
                margin-bottom: 15px;
            }
            
            .highlight-box {
                padding: 15px;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
                min-height: auto;
            }
            
            .sidebar {
                padding: 30px 25px;
            }
            
            .main-content {
                padding: 30px 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <aside class="sidebar">
            <div class="profile-section">
                <div class="photo-placeholder">EC</div>
                <h1 class="name">Efren Cruz</h1>
                <p class="title">Especialista en Nómina & Tecnología</p>
            </div>
            
            <div class="contact-info">
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <span>San Pedro Tlaquepaque, Jalisco</span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <svg class="contact-icon" viewBox="0 0 24 24">
                        <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                    </svg>
                    <span>+52 331 161 1322</span>
                </div>
            </div>
            
            <section class="section">
                <h2 class="section-title">Perfil Profesional</h2>
                <p class="profile-text">
                    Especialista en nómina con más de 10 años de experiencia integral en todo el ciclo: pre-nómina, cálculos, finiquitos y cumplimiento ante IMSS, Infonavit, Fonacot, ISR, ISN, REPSE, ICSOE, SISUB y SIROC.
                </p>
                <p class="profile-text">
                    He liderado equipos, creado áreas desde cero y acompañado a clientes en esquemas de subcontratación y maquila. Convencido de que la tecnología debe ayudar a lograr una nómina feliz… o al menos, sin quejas.
                </p>
            </section>
            
            <div class="sidebar-section">
                <h3 class="sidebar-title">Formación</h3>
                <div class="education-item">
                    <h4 class="degree">Lic. Contaduría Pública</h4>
                    <p class="university">Universidad de Guadalajara</p>
                    <p class="date-small">2006 - 2010</p>
                </div>
            </div>
            
            <div class="sidebar-section">
                <h3 class="sidebar-title">Habilidades</h3>
                <div class="skill-item">
                    <div class="skill-name">Cálculo de Nómina</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 100%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">IMSS, Infonavit, Fonacot</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 100%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">REPSE, ICSOE, SISUB</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 95%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">SQL, Python, Power BI</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 80%"></div>
                    </div>
                </div>
                <div class="skill-item">
                    <div class="skill-name">Liderazgo de Equipos</div>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: 90%"></div>
                    </div>
                </div>
            </div>
        </aside>

        <main class="main-content">
            <section class="section">
                <h2 class="section-title">Experiencia Profesional</h2>
                
                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Especialista en Nómina</h3>
                            <p class="company">Freelance</p>
                        </div>
                        <span class="date">Dic 2024 – Actualidad</span>
                    </div>
                    <ul class="job-description">
                        <li>Asesor en cumplimiento y soporte técnico para desarrollo del software <strong>Lynx Cloud</strong></li>
                        <li>Validación de cálculos de nómina y definición de requisitos funcionales</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Coordinador de Nóminas</h3>
                            <p class="company">GC Studio</p>
                        </div>
                        <span class="date">Ago 2021 – Nov 2024</span>
                    </div>
                    <ul class="job-description">
                        <li>Responsable del área de nóminas en despacho fiscal</li>
                        <li>Creación del departamento y gestión bajo maquila</li>
                        <li>Asesoría y cumplimiento ante IMSS, INFONAVIT, INFONACOT, ISN, REPSE, ICSOE y SISUB</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Gerente de Nóminas</h3>
                            <p class="company">Corporativo BOSSC</p>
                        </div>
                        <span class="date">May 2017 – Ago 2021</span>
                    </div>
                    <ul class="job-description">
                        <li>Funciones equivalentes a GC Studio</li>
                        <li>Coordinación de equipo y atención directa a clientes</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Supervisor de Nóminas</h3>
                            <p class="company">Human Services 21</p>
                        </div>
                        <span class="date">Oct 2011 – May 2017</span>
                    </div>
                    <ul class="job-description">
                        <li>Promovido de analista a supervisor</li>
                        <li>Coordinación de 4 analistas y validación de nóminas especiales</li>
                        <li>Control de headcount vinculado al cobro del servicio</li>
                    </ul>
                </div>
            </section>

            <section class="section">
                <h2 class="section-title">Proyectos e Intereses</h2>
                <div class="tools-grid">
                    <div class="tool-item">
                        <h3 class="tool-name">PTU Tool</h3>
                        <p class="tool-description">Herramienta para cálculo de Participación de Trabajadores en las Utilidades</p>
                        <a href="https://ptu-tool.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                    <div class="tool-item">
                        <h3 class="tool-name">Nominante DIOT</h3>
                        <p class="tool-description">Herramienta para Declaración Informativa de Operaciones con Terceros</p>
                        <a href="https://nominantediot.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                </div>
                <div class="highlight-box">
                    <h3 class="highlight-title">Enfoque Profesional</h3>
                    <p class="profile-text">
                        Automatización y eficiencia en procesos de nómina con mejora continua aplicando tecnología.
                    </p>
                </div>
            </section>
        </main>
    </div>
</body>
</html>