<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - <PERSON> Profesional</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #2b6cb8 0%, #1e4a72 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #1e4a72 0%, #2b6cb8 100%);
            color: white;
            padding: 60px 50px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 6s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        .name {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #60a5fa, #93c5fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .title {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            font-size: 1.1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .contact-icon {
            width: 20px;
            height: 20px;
            fill: #60a5fa;
        }
        
        .main-content {
            padding: 50px;
        }
        
        .section {
            margin-bottom: 50px;
        }
        
        .section-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1e4a72;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 3px solid #2b6cb8;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: #60a5fa;
        }
        
        .profile-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #4a5568;
            text-align: justify;
        }
        
        .experience-item {
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 15px;
            border-left: 5px solid #2b6cb8;
            position: relative;
        }
        
        .experience-item::before {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #2b6cb8, #1e4a72);
            border-radius: 50%;
            opacity: 0.1;
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .job-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e4a72;
        }
        
        .company {
            font-size: 1.1rem;
            color: #2b6cb8;
            font-weight: 500;
        }
        
        .date {
            font-size: 0.95rem;
            color: #718096;
            font-weight: 500;
            background: #e2e8f0;
            padding: 5px 15px;
            border-radius: 20px;
        }
        
        .job-description {
            list-style: none;
            padding: 0;
        }
        
        .job-description li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
            color: #4a5568;
        }
        
        .job-description li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #2b6cb8;
            font-size: 0.8rem;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .skill-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }
        
        .skill-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #1e4a72;
        }
        
        .skill-bar {
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .skill-progress {
            height: 100%;
            background: linear-gradient(90deg, #2b6cb8 0%, #1e4a72 100%);
            border-radius: 4px;
            transition: width 2s ease;
        }
        
        .skill-percentage {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 5px;
        }
        
        .education-item {
            background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #10b981;
        }
        
        .degree {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e4a72;
            margin-bottom: 10px;
        }
        
        .university {
            font-size: 1.1rem;
            color: #059669;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .tool-item {
            background: linear-gradient(135deg, #f0f9ff 0%, #bfdbfe 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #2b6cb8;
        }
        
        .tool-name {
            font-weight: 600;
            color: #1e4a72;
            margin-bottom: 10px;
        }
        
        .tool-link {
            color: #2b6cb8;
            text-decoration: none;
            font-weight: 500;
        }
        
        .tool-link:hover {
            text-decoration: underline;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #3b82f6;
            margin-top: 30px;
        }
        
        .highlight-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e4a72;
            margin-bottom: 15px;
        }

        .pdf-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #2b6cb8 0%, #1e4a72 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 10px 25px rgba(43, 108, 184, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pdf-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(43, 108, 184, 0.4);
        }

        .pdf-button:active {
            transform: translateY(0);
        }

        .pdf-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
        
        @media (max-width: 768px) {
            .header {
                padding: 40px 30px;
            }
            
            .name {
                font-size: 2.5rem;
            }
            
            .main-content {
                padding: 30px;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 15px;
            }
            
            .job-header {
                flex-direction: column;
                gap: 10px;
            }
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
                margin: 0;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                max-width: none;
                margin: 0;
                border-radius: 0;
            }

            .header {
                padding: 20px 30px;
                page-break-inside: avoid;
            }

            .header::before {
                display: none;
            }

            .name {
                font-size: 2.2rem;
                margin-bottom: 5px;
            }

            .title {
                font-size: 1.1rem;
                margin-bottom: 15px;
            }

            .contact-info {
                gap: 15px;
                font-size: 0.9rem;
            }

            .main-content {
                padding: 20px 30px;
            }

            .section {
                margin-bottom: 25px;
                page-break-inside: avoid;
            }

            .section-title {
                font-size: 1.3rem;
                margin-bottom: 12px;
                padding-bottom: 5px;
            }

            .profile-text {
                font-size: 0.9rem;
                line-height: 1.4;
                margin-bottom: 8px;
            }

            .experience-item {
                margin-bottom: 15px;
                padding: 15px;
                page-break-inside: avoid;
            }

            .experience-item::before {
                display: none;
            }

            .job-title {
                font-size: 1rem;
            }

            .company {
                font-size: 0.9rem;
            }

            .date {
                font-size: 0.8rem;
                padding: 3px 10px;
            }

            .job-description {
                margin-top: 8px;
            }

            .job-description li {
                margin-bottom: 4px;
                font-size: 0.85rem;
                line-height: 1.3;
            }

            .education-item {
                padding: 15px;
                margin-bottom: 15px;
            }

            .degree {
                font-size: 1rem;
                margin-bottom: 5px;
            }

            .university {
                font-size: 0.9rem;
                margin-bottom: 3px;
            }

            .skills-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .skill-item {
                padding: 10px;
            }

            .skill-name {
                font-size: 0.85rem;
                margin-bottom: 5px;
            }

            .skill-bar {
                height: 6px;
            }

            .skill-percentage {
                font-size: 0.75rem;
                margin-top: 3px;
            }

            .tools-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .tool-item {
                padding: 12px;
            }

            .tool-name {
                font-size: 0.9rem;
                margin-bottom: 5px;
            }

            .tool-item p {
                font-size: 0.8rem;
                margin-bottom: 5px;
            }

            .tool-link {
                font-size: 0.8rem;
            }

            .highlight-box {
                padding: 15px;
                margin-top: 15px;
            }

            .highlight-title {
                font-size: 1rem;
                margin-bottom: 8px;
            }

            .pdf-button {
                display: none;
            }

            /* Forzar que todo quepa en una página */
            .container {
                height: auto;
                max-height: none;
            }

            /* Evitar saltos de página innecesarios */
            h1, h2, h3 {
                page-break-after: avoid;
            }

            /* Compactar más el contenido */
            br {
                display: none;
            }
        }
    </style>
</head>
<body>
    <button class="pdf-button" onclick="exportToPDF()">
        <svg class="pdf-icon" viewBox="0 0 24 24">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        Exportar PDF
    </button>

    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1 class="name">Efren Cruz</h1>
                <p class="title">Especialista en Nómina & Tecnología</p>
                <div class="contact-info">
                    <div class="contact-item">
                        <svg class="contact-icon" viewBox="0 0 24 24">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <span>San Pedro Tlaquepaque, Jalisco</span>
                    </div>
                    <div class="contact-item">
                        <svg class="contact-icon" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <svg class="contact-icon" viewBox="0 0 24 24">
                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                        <span>+52 331 161 1322</span>
                    </div>
                </div>
            </div>
        </header>

        <main class="main-content">
            <section class="section">
                <h2 class="section-title">Perfil profesional</h2>
                <p class="profile-text">
                    Especialista en nómina con más de 10 años de experiencia integral en todo el ciclo: pre-nómina, cálculos, finiquitos y cumplimiento ante IMSS, Infonavit, Fonacot, ISR, ISN, REPSE, ICSOE, SISUB y SIROC. He liderado equipos, creado áreas desde cero y acompañado a clientes en esquemas de subcontratación y maquila. Convencido de que la tecnología debe ayudar a lograr una nómina feliz… o al menos, sin quejas.
                </p>
            </section>

            <section class="section">
                <h2 class="section-title">Experiencia Profesional</h2>
                
                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Especialista en Nómina</h3>
                            <p class="company">Freelance</p>
                        </div>
                        <span class="date">Dic 2024 – Actualidad</span>
                    </div>
                    <ul class="job-description">
                        <li>Asesor en cumplimiento y soporte técnico para desarrollo del software <strong>Lynx Cloud</strong></li>
                        <li>Validación de cálculos de nómina y definición de requisitos funcionales</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Coordinador de Nóminas</h3>
                            <p class="company">GC Studio</p>
                        </div>
                        <span class="date">Ago 2021 – Nov 2024</span>
                    </div>
                    <ul class="job-description">
                        <li>Responsable del área de nóminas en despacho fiscal, creación del departamento y gestión bajo maquila</li>
                        <li>Asesoría y cumplimiento ante IMSS, INFONAVIT, INFONACOT, ISN, REPSE, ICSOE y SISUB</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Gerente de Nóminas</h3>
                            <p class="company">Corporativo BOSSC</p>
                        </div>
                        <span class="date">May 2017 – Ago 2021</span>
                    </div>
                    <ul class="job-description">
                        <li>Funciones equivalentes a GC Studio</li>
                        <li>Coordinación de equipo y atención directa a clientes</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <h3 class="job-title">Supervisor de Nóminas</h3>
                            <p class="company">Human Services 21</p>
                        </div>
                        <span class="date">Oct 2011 – May 2017</span>
                    </div>
                    <ul class="job-description">
                        <li>Promovido de analista a supervisor</li>
                        <li>Coordinación de 4 analistas y validación de nóminas especiales (aguinaldo, PTU, fondo de ahorro)</li>
                        <li>Control de headcount vinculado al cobro del servicio</li>
                    </ul>
                </div>
            </section>

            <section class="section">
                <h2 class="section-title">Formación Académica</h2>
                <div class="education-item">
                    <h3 class="degree">Licenciatura en Trabajo Social (Pasante)</h3>
                    <p class="university">Universidad de Guadalajara</p>
                    <p class="date">Feb 2006 – Feb 2010</p>
                </div>
            </section>

            <section class="section">
                <h2 class="section-title">Habilidades y herramientas</h2>
                <div class="skills-grid">
                    <div class="skill-item">
                        <div class="skill-name">Pre-nómina y Cálculo Integral</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 100%"></div>
                        </div>
                        <div class="skill-percentage">100%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">IMSS, Infonavit, Fonacot</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 100%"></div>
                        </div>
                        <div class="skill-percentage">100%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">REPSE, ICSOE, SISUB, SIROC</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 95%"></div>
                        </div>
                        <div class="skill-percentage">95%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">SUA, IDSE, Sistemas Internos</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                        <div class="skill-percentage">90%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">SQL, Python, Power BI</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 80%"></div>
                        </div>
                        <div class="skill-percentage">80%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Liderazgo de Equipos</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 90%"></div>
                        </div>
                        <div class="skill-percentage">90%</div>
                    </div>
                    <div class="skill-item">
                        <div class="skill-name">Atención al Cliente</div>
                        <div class="skill-bar">
                            <div class="skill-progress" style="width: 100%"></div>
                        </div>
                        <div class="skill-percentage">100%</div>
                    </div>
                </div>
            </section>

            <section class="section">
                <h2 class="section-title">Intereses profesionales</h2>
                <div class="tools-grid">
                    <div class="tool-item">
                        <h3 class="tool-name">PTU Tool</h3>
                        <p>Herramienta para cálculo de Participación de Trabajadores en las Utilidades</p>
                        <a href="https://ptu-tool.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                    <div class="tool-item">
                        <h3 class="tool-name">Nominante DIOT</h3>
                        <p>Herramienta para Declaración Informativa de Operaciones con Terceros</p>
                        <a href="https://nominantediot.streamlit.app/" class="tool-link" target="_blank">Ver Herramienta</a>
                    </div>
                </div>
                <div class="highlight-box">
                    <h3 class="highlight-title">Enfoque profesional</h3>
                    <p class="profile-text">
                        Automatización y eficiencia en procesos de nómina con mejora continua aplicando tecnología.
                    </p>
                </div>
            </section>
        </main>
    </div>

    <script>
        function exportToPDF() {
            // Crear un mensaje de información
            const originalButton = document.querySelector('.pdf-button');
            originalButton.innerHTML = `
                <svg class="pdf-icon" viewBox="0 0 24 24">
                    <path d="M12,4V2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
                Generando...
            `;
            originalButton.style.pointerEvents = 'none';

            // Usar la función de impresión del navegador
            setTimeout(() => {
                window.print();

                // Restaurar el botón después de un momento
                setTimeout(() => {
                    originalButton.innerHTML = `
                        <svg class="pdf-icon" viewBox="0 0 24 24">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        Exportar PDF
                    `;
                    originalButton.style.pointerEvents = 'auto';
                }, 1000);
            }, 500);
        }

        // Animación de las barras de habilidades al cargar la página
        window.addEventListener('load', function() {
            const skillBars = document.querySelectorAll('.skill-progress');
            skillBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>