"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .outputcontentchunks import OutputContentChunks, OutputContentChunksTypedDict
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing import Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


MessageOutputEventType = Literal["message.output.delta"]

MessageOutputEventRole = Literal["assistant"]

MessageOutputEventContentTypedDict = TypeAliasType(
    "MessageOutputEventContentTypedDict", Union[str, OutputContentChunksTypedDict]
)


MessageOutputEventContent = TypeAliasType(
    "MessageOutputEventContent", Union[str, OutputContentChunks]
)


class MessageOutputEventTypedDict(TypedDict):
    id: str
    content: MessageOutputEventContentTypedDict
    type: NotRequired[MessageOutputEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]
    content_index: NotRequired[int]
    model: NotRequired[Nullable[str]]
    agent_id: NotRequired[Nullable[str]]
    role: NotRequired[MessageOutputEventRole]


class MessageOutputEvent(BaseModel):
    id: str

    content: MessageOutputEventContent

    type: Optional[MessageOutputEventType] = "message.output.delta"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0

    content_index: Optional[int] = 0

    model: OptionalNullable[str] = UNSET

    agent_id: OptionalNullable[str] = UNSET

    role: Optional[MessageOutputEventRole] = "assistant"

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "type",
            "created_at",
            "output_index",
            "content_index",
            "model",
            "agent_id",
            "role",
        ]
        nullable_fields = ["model", "agent_id"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
